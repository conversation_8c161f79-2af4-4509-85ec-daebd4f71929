from datetime import datetime, timedelta
import jwt
from fastapi import HTTPException, status
from typing import Optional, Union, Dict, List
from sqlalchemy.orm import Session
from models import Task, Project, Merchant, User  # 添加User模型导入
from schemas import TaskCreate, TaskUpdate, UserCreate  # 添加User相关的导入


def get_merchant(db: Session, merchant_id: int):
    return db.query(Merchant).filter(Merchant.id == merchant_id).first()


def get_tasks(db: Session, skip: int = 0, limit: int = 100, project_id: Optional[int] = None):
    query = db.query(Task)
    if project_id:
        query = query.filter(Task.project_id == project_id)
    return query.offset(skip).limit(limit).all()

def get_task(db: Session, task_id: int):
    return db.query(Task).filter(Task.id == task_id).first()

def create_task(db: Session, task: TaskCreate):
    db_task = Task(**task.dict())
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task

def update_task(task_id: int, task: TaskUpdate, db: Session):  # 修改参数类型为TaskUpdate
    db_task = db.query(Task).filter(Task.id == task_id).first()
    if db_task:
        for key, value in task.dict().items():
            setattr(db_task, key, value)
        db.commit()
        db.refresh(db_task)
        return db_task
    return None


def delete_task(db: Session, task_id: int):
    db_task = db.query(Task).filter(Task.id == task_id).first()
    if db_task:
        db.delete(db_task)
        db.commit()
        return db_task
    return None

def get_task_stats(db: Session, merchant_id: int, time_range: str, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict:
    # 这里实现统计逻辑，根据时间范围查询任务数据
    query = db.query(Task).join(Project).filter(Project.merchant_id == merchant_id)
    
    if time_range == "today":
        today = datetime.today().date()
        query = query.filter(Task.end_date >= today, Task.end_date <= today + timedelta(days=1))
    elif time_range == "week":
        today = datetime.today().date()
        week_start = today - timedelta(days=today.weekday())
        query = query.filter(Task.end_date >= week_start, Task.end_date <= today + timedelta(days=7))
    elif time_range == "month":
        today = datetime.today().date()
        month_start = today.replace(day=1)
        next_month = (today.replace(day=28) + timedelta(days=4)).replace(day=1)
        query = query.filter(Task.end_date >= month_start, Task.end_date <= next_month - timedelta(days=1))
    elif time_range == "custom" and start_date and end_date:
        query = query.filter(Task.end_date >= start_date, Task.end_date <= end_date)
    
    tasks = query.all()
    
    total_tasks = len(tasks)
    # 使用更安全的方式处理可能为None的progress字段
    completed_tasks = 0
    for task in tasks:
        progress = getattr(task, 'progress', None)
        if progress is not None and float(progress) >= 99.99:
            completed_tasks += 1
            
    completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    return {
        "total_tasks": total_tasks,
        "completed_tasks": completed_tasks,
        "completion_rate": round(completion_rate, 2),
        "time_range": time_range
    }

def get_user(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()

def get_users(db: Session, skip: int = 0, limit: int = 100):
    return db.query(User).offset(skip).limit(limit).all()

def create_user(db: Session, user: UserCreate):
    db_user = User(**user.dict())
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(user_id: int, user: UserCreate, db: Session):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        for key, value in user.dict().items():
            setattr(db_user, key, value)
        db.commit()
        db.refresh(db_user)
        return db_user
    return None

def delete_user(db: Session, user_id: int):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        db.delete(db_user)
        db.commit()
        return db_user
    return None

import asyncio

async def authenticate_user(db: Session, username: str, password: str):
    user = db.query(User).filter(User.username == username).first()
    if user is not None and str(user.password) == str(password):  # 显式转换为字符串比较
        return user
    return None
